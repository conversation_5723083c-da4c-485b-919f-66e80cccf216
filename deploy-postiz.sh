#!/bin/bash

echo "🚀 在现有n8n服务器上部署Postiz..."

# 检查是否在正确的目录
if [ ! -f "postiz-docker-compose.yml" ]; then
    echo "❌ 错误：请确保在包含postiz-docker-compose.yml的目录中运行此脚本"
    exit 1
fi

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ 错误：Docker未运行或无权限访问"
    exit 1
fi

echo "📋 部署前检查..."

# 检查端口5000是否被占用
if netstat -tuln | grep -q ":5000 "; then
    echo "⚠️  警告：端口5000已被占用，请检查是否有其他服务在使用"
    echo "当前使用端口5000的进程："
    sudo lsof -i :5000
    read -p "是否继续？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 检查可用内存
available_memory=$(free -m | awk 'NR==2{printf "%.0f", $7}')
echo "📊 可用内存: ${available_memory}MB"

if [ "$available_memory" -lt 500 ]; then
    echo "⚠️  警告：可用内存较少，建议添加swap空间"
    read -p "是否继续？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "💡 建议运行以下命令添加swap："
        echo "sudo fallocate -l 2G /swapfile"
        echo "sudo chmod 600 /swapfile"
        echo "sudo mkswap /swapfile"
        echo "sudo swapon /swapfile"
        exit 1
    fi
fi

echo "🔧 启动Postiz服务..."

# 使用指定的compose文件启动
sudo docker compose -f postiz-docker-compose.yml up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 检查服务状态
echo "📊 检查服务状态..."
sudo docker compose -f postiz-docker-compose.yml ps

# 检查健康状态
echo "🏥 检查服务健康状态..."
for i in {1..12}; do
    if curl -f http://localhost:5000/health > /dev/null 2>&1; then
        echo "✅ Postiz服务已启动并运行正常！"
        break
    elif [ $i -eq 12 ]; then
        echo "❌ Postiz服务启动超时，请检查日志："
        echo "sudo docker compose -f postiz-docker-compose.yml logs"
        exit 1
    else
        echo "⏳ 等待服务启动... ($i/12)"
        sleep 10
    fi
done

echo ""
echo "🎉 Postiz部署完成！"
echo ""
echo "📝 下一步操作："
echo "1. 确保DNS记录指向此服务器："
echo "   postiz.yourdomain.com -> $(curl -s ifconfig.me)"
echo ""
echo "2. 重启Caddy以应用新配置："
echo "   cd ~/n8n-docker-caddy/"
echo "   sudo docker compose restart caddy"
echo ""
echo "3. 访问Postiz："
echo "   https://postiz.yourdomain.com"
echo ""
echo "📊 资源使用情况："
echo "内存使用："
free -h
echo ""
echo "磁盘使用："
df -h
echo ""
echo "🔍 查看日志："
echo "sudo docker compose -f postiz-docker-compose.yml logs -f"
