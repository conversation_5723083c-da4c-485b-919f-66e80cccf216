# n8n + Postiz 共存部署方案

## 🎯 架构设计

```
Internet
    ↓
Nginx反向代理 (80/443)
    ├── n8n.yourdomain.com → localhost:5678
    └── postiz.yourdomain.com → localhost:5000
```

## 📋 部署步骤

### 1. 检查当前n8n配置

```bash
# 查看当前运行的容器
docker ps

# 查看n8n配置
docker inspect n8n_container_name
```

### 2. 创建Postiz项目目录

```bash
# 在不同目录创建Postiz项目
mkdir -p /opt/postiz
cd /opt/postiz
```

### 3. 创建Postiz Docker Compose配置

创建 `/opt/postiz/docker-compose.yml`:

```yaml
version: '3.8'

services:
  postiz:
    image: ghcr.io/gitroomhq/postiz-app:latest
    container_name: postiz-app
    restart: always
    environment:
      # 使用子域名
      MAIN_URL: "https://postiz.yourdomain.com"
      FRONTEND_URL: "https://postiz.yourdomain.com"
      NEXT_PUBLIC_BACKEND_URL: "https://postiz.yourdomain.com/api"
      
      # 生成新的JWT密钥
      JWT_SECRET: "postiz-super-secret-jwt-key-different-from-n8n"
      
      # 数据库配置
      DATABASE_URL: "********************************************************************/postiz-db"
      REDIS_URL: "redis://postiz-redis:6379"
      BACKEND_INTERNAL_URL: "http://localhost:3000"
      
      # 必需配置
      IS_GENERAL: "true"
      DISABLE_REGISTRATION: "false"
      
      # 文件存储
      STORAGE_PROVIDER: "local"
      UPLOAD_DIRECTORY: "/uploads"
      NEXT_PUBLIC_UPLOAD_DIRECTORY: "/uploads"
      
    volumes:
      - postiz-config:/config/
      - postiz-uploads:/uploads/
    ports:
      - "5000:5000"  # 不与n8n冲突
    networks:
      - postiz-network
    depends_on:
      postiz-postgres:
        condition: service_healthy
      postiz-redis:
        condition: service_healthy

  postiz-postgres:
    image: postgres:17-alpine
    container_name: postiz-postgres
    restart: always
    environment:
      POSTGRES_PASSWORD: postiz-secure-password
      POSTGRES_USER: postiz-user
      POSTGRES_DB: postiz-db
    volumes:
      - postiz-postgres-data:/var/lib/postgresql/data
    networks:
      - postiz-network
    healthcheck:
      test: pg_isready -U postiz-user -d postiz-db
      interval: 10s
      timeout: 3s
      retries: 3

  postiz-redis:
    image: redis:7.2-alpine
    container_name: postiz-redis
    restart: always
    command: redis-server --maxmemory 128mb --maxmemory-policy allkeys-lru
    healthcheck:
      test: redis-cli ping
      interval: 10s
      timeout: 3s
      retries: 3
    volumes:
      - postiz-redis-data:/data
    networks:
      - postiz-network

volumes:
  postiz-postgres-data:
    external: false
  postiz-redis-data:
    external: false
  postiz-config:
    external: false
  postiz-uploads:
    external: false

networks:
  postiz-network:
    external: false
```

### 4. 配置Nginx反向代理

更新Nginx配置以支持两个应用：

```bash
# 备份现有配置
sudo cp /etc/nginx/sites-available/default /etc/nginx/sites-available/default.backup

# 创建新的配置文件
sudo nano /etc/nginx/sites-available/multi-apps
```

Nginx配置内容：

```nginx
# n8n配置
server {
    listen 80;
    server_name n8n.yourdomain.com;
    
    location / {
        proxy_pass http://localhost:5678;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket支持（n8n需要）
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}

# Postiz配置
server {
    listen 80;
    server_name postiz.yourdomain.com;
    
    location / {
        proxy_pass http://localhost:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 增加超时时间
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
```

```bash
# 启用新配置
sudo ln -s /etc/nginx/sites-available/multi-apps /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 5. 配置SSL证书

```bash
# 为两个域名申请证书
sudo certbot --nginx -d n8n.yourdomain.com -d postiz.yourdomain.com
```

### 6. 启动Postiz

```bash
cd /opt/postiz
docker compose up -d

# 查看启动状态
docker compose logs -f
```

## 🔧 资源优化配置

### 限制容器资源使用

在docker-compose.yml中添加资源限制：

```yaml
services:
  postiz:
    # ... 其他配置
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
  
  postiz-postgres:
    # ... 其他配置
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M
  
  postiz-redis:
    # ... 其他配置
    deploy:
      resources:
        limits:
          memory: 128M
        reservations:
          memory: 64M
```

### 系统优化

```bash
# 启用swap（如果没有）
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
echo '/swapfile none swap sw 0 0' | sudo tee -a /etc/fstab

# 优化系统参数
echo 'vm.swappiness=10' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

## 🔍 监控和管理

### 查看资源使用情况

```bash
# 查看容器资源使用
docker stats

# 查看系统资源
htop
free -h
df -h
```

### 管理脚本

创建管理脚本 `/opt/manage-apps.sh`:

```bash
#!/bin/bash

case "$1" in
    "status")
        echo "=== n8n Status ==="
        docker ps | grep n8n
        echo "=== Postiz Status ==="
        cd /opt/postiz && docker compose ps
        ;;
    "restart-n8n")
        docker restart n8n_container_name
        ;;
    "restart-postiz")
        cd /opt/postiz && docker compose restart
        ;;
    "logs-n8n")
        docker logs -f n8n_container_name
        ;;
    "logs-postiz")
        cd /opt/postiz && docker compose logs -f
        ;;
    "backup")
        # 备份脚本
        mkdir -p /opt/backups/$(date +%Y%m%d)
        # n8n备份
        docker exec n8n_container_name n8n export:workflow --backup --output=/data/backup.json
        # Postiz备份
        cd /opt/postiz && docker compose exec postiz-postgres pg_dump -U postiz-user postiz-db > /opt/backups/$(date +%Y%m%d)/postiz-backup.sql
        ;;
    *)
        echo "Usage: $0 {status|restart-n8n|restart-postiz|logs-n8n|logs-postiz|backup}"
        ;;
esac
```

```bash
chmod +x /opt/manage-apps.sh
```

## 🌐 DNS配置

在您的域名提供商处添加A记录：
- `n8n.yourdomain.com` → 您的服务器IP
- `postiz.yourdomain.com` → 您的服务器IP

## 📊 访问地址

- **n8n**: https://n8n.yourdomain.com
- **Postiz**: https://postiz.yourdomain.com

## ⚠️ 注意事项

1. **内存监控**: 定期检查内存使用，必要时升级服务器
2. **端口冲突**: 确保端口不冲突（n8n:5678, Postiz:5000）
3. **数据备份**: 两个应用都要定期备份
4. **更新策略**: 分别更新，避免同时重启
5. **日志管理**: 定期清理日志文件

## 🚀 升级建议

如果资源紧张，建议升级到：
- **4GB RAM / 2 vCPU** - 更好的性能
- **80GB 硬盘** - 更多存储空间

这样可以确保两个应用都能稳定运行！
