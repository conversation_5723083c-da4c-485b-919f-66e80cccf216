version: '3.8'

services:
  postiz:
    image: ghcr.io/gitroomhq/postiz-app:latest
    container_name: postiz-app
    restart: always
    environment:
      # 替换为您的Postiz域名
      MAIN_URL: "https://postiz.yourdomain.com"
      FRONTEND_URL: "https://postiz.yourdomain.com"
      NEXT_PUBLIC_BACKEND_URL: "https://postiz.yourdomain.com/api"
      
      # 生成唯一的JWT密钥（与n8n不同）
      JWT_SECRET: "postiz-unique-jwt-secret-key-different-from-n8n"
      
      # 数据库配置
      DATABASE_URL: "********************************************************************/postiz-db"
      REDIS_URL: "redis://postiz-redis:6379"
      BACKEND_INTERNAL_URL: "http://localhost:3000"
      
      # 必需配置
      IS_GENERAL: "true"
      DISABLE_REGISTRATION: "false"
      
      # 文件存储
      STORAGE_PROVIDER: "local"
      UPLOAD_DIRECTORY: "/uploads"
      NEXT_PUBLIC_UPLOAD_DIRECTORY: "/uploads"
      
    volumes:
      - postiz-config:/config/
      - postiz-uploads:/uploads/
    ports:
      - "5000:5000"  # 不与n8n的5678冲突
    networks:
      - postiz-network
    depends_on:
      postiz-postgres:
        condition: service_healthy
      postiz-redis:
        condition: service_healthy
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  postiz-postgres:
    image: postgres:17-alpine
    container_name: postiz-postgres
    restart: always
    environment:
      POSTGRES_PASSWORD: postiz-secure-password
      POSTGRES_USER: postiz-user
      POSTGRES_DB: postiz-db
    volumes:
      - postiz-postgres-data:/var/lib/postgresql/data
    networks:
      - postiz-network
    healthcheck:
      test: pg_isready -U postiz-user -d postiz-db
      interval: 10s
      timeout: 3s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  postiz-redis:
    image: redis:7.2-alpine
    container_name: postiz-redis
    restart: always
    command: redis-server --maxmemory 100mb --maxmemory-policy allkeys-lru
    volumes:
      - postiz-redis-data:/data
    networks:
      - postiz-network
    healthcheck:
      test: redis-cli ping
      interval: 10s
      timeout: 3s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 128M
        reservations:
          memory: 64M

volumes:
  postiz-postgres-data:
    external: false
  postiz-redis-data:
    external: false
  postiz-config:
    external: false
  postiz-uploads:
    external: false

networks:
  postiz-network:
    external: false
