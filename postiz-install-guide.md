# Postiz在DigitalOcean上的安装指南

## 🎯 推荐配置

### DigitalOcean Droplet规格
- **最低配置**: 2GB RAM, 2 vCPUs, 50GB SSD
- **推荐配置**: 4GB RAM, 2 vCPUs, 80GB SSD
- **操作系统**: Ubuntu 24.04 LTS

## 📋 安装步骤

### 1. 准备服务器环境

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装Docker Compose
sudo apt install docker-compose-plugin -y

# 添加用户到docker组
sudo usermod -aG docker $USER
newgrp docker

# 验证安装
docker --version
docker compose version
```

### 2. 创建项目目录

```bash
mkdir -p /opt/postiz
cd /opt/postiz
```

### 3. 创建Docker Compose配置

创建 `docker-compose.yml` 文件：

```yaml
services:
  postiz:
    image: ghcr.io/gitroomhq/postiz-app:latest
    container_name: postiz
    restart: always
    environment:
      # 替换为您的域名
      MAIN_URL: "https://your-domain.com"
      FRONTEND_URL: "https://your-domain.com"
      NEXT_PUBLIC_BACKEND_URL: "https://your-domain.com/api"
      
      # 生成随机JWT密钥
      JWT_SECRET: "your-super-secret-jwt-key-here-make-it-long-and-random"
      
      # 数据库配置
      DATABASE_URL: "*************************************************************/postiz-db-local"
      REDIS_URL: "redis://postiz-redis:6379"
      BACKEND_INTERNAL_URL: "http://localhost:3000"
      
      # 必需配置
      IS_GENERAL: "true"
      DISABLE_REGISTRATION: "false"
      
      # 文件存储
      STORAGE_PROVIDER: "local"
      UPLOAD_DIRECTORY: "/uploads"
      NEXT_PUBLIC_UPLOAD_DIRECTORY: "/uploads"
      
      # 如果没有HTTPS，添加此项
      # NOT_SECURED: "true"
      
    volumes:
      - postiz-config:/config/
      - postiz-uploads:/uploads/
    ports:
      - "5000:5000"
    networks:
      - postiz-network
    depends_on:
      postiz-postgres:
        condition: service_healthy
      postiz-redis:
        condition: service_healthy

  postiz-postgres:
    image: postgres:17-alpine
    container_name: postiz-postgres
    restart: always
    environment:
      POSTGRES_PASSWORD: postiz-password
      POSTGRES_USER: postiz-user
      POSTGRES_DB: postiz-db-local
    volumes:
      - postgres-volume:/var/lib/postgresql/data
    networks:
      - postiz-network
    healthcheck:
      test: pg_isready -U postiz-user -d postiz-db-local
      interval: 10s
      timeout: 3s
      retries: 3

  postiz-redis:
    image: redis:7.2
    container_name: postiz-redis
    restart: always
    healthcheck:
      test: redis-cli ping
      interval: 10s
      timeout: 3s
      retries: 3
    volumes:
      - postiz-redis-data:/data
    networks:
      - postiz-network

volumes:
  postgres-volume:
    external: false
  postiz-redis-data:
    external: false
  postiz-config:
    external: false
  postiz-uploads:
    external: false

networks:
  postiz-network:
    external: false
```

### 4. 启动服务

```bash
# 启动所有服务
docker compose up -d

# 查看日志
docker compose logs -f

# 检查服务状态
docker compose ps
```

## 🌐 域名和反向代理配置

### 选项1：使用Nginx（推荐）

```bash
# 安装Nginx
sudo apt install nginx -y

# 创建配置文件
sudo nano /etc/nginx/sites-available/postiz
```

Nginx配置内容：
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

```bash
# 启用站点
sudo ln -s /etc/nginx/sites-available/postiz /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 选项2：使用Caddy（自动HTTPS）

```bash
# 安装Caddy
sudo apt install -y debian-keyring debian-archive-keyring apt-transport-https
curl -1sLf 'https://dl.cloudsmith.io/public/caddy/stable/gpg.key' | sudo gpg --dearmor -o /usr/share/keyrings/caddy-stable-archive-keyring.gpg
curl -1sLf 'https://dl.cloudsmith.io/public/caddy/stable/debian.deb.txt' | sudo tee /etc/apt/sources.list.d/caddy-stable.list
sudo apt update
sudo apt install caddy

# 创建Caddyfile
sudo nano /etc/caddy/Caddyfile
```

Caddyfile内容：
```
your-domain.com {
    reverse_proxy localhost:5000
}
```

```bash
sudo systemctl reload caddy
```

## 🔒 SSL证书配置

### 使用Let's Encrypt（免费）

```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx -y

# 获取证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 🔧 防火墙配置

```bash
# 配置UFW防火墙
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable
```

## 📊 监控和维护

### 查看服务状态
```bash
# 检查容器状态
docker compose ps

# 查看日志
docker compose logs postiz
docker compose logs postiz-postgres
docker compose logs postiz-redis

# 重启服务
docker compose restart postiz
```

### 备份数据
```bash
# 备份数据库
docker exec postiz-postgres pg_dump -U postiz-user postiz-db-local > backup.sql

# 备份上传文件
sudo tar -czf uploads-backup.tar.gz /var/lib/docker/volumes/postiz_postiz-uploads
```

## 🎯 访问应用

1. 打开浏览器访问: `https://your-domain.com`
2. 创建管理员账户
3. 配置社交媒体平台连接

## ⚠️ 注意事项

1. **域名配置**: 必须将域名指向您的DigitalOcean Droplet IP
2. **JWT密钥**: 生成强随机密钥，不要使用示例中的值
3. **数据库密码**: 修改默认的数据库密码
4. **防火墙**: 只开放必要的端口
5. **备份**: 定期备份数据库和上传文件
6. **更新**: 定期更新Docker镜像

## 🔄 更新Postiz

```bash
cd /opt/postiz
docker compose pull
docker compose down
docker compose up -d
```
